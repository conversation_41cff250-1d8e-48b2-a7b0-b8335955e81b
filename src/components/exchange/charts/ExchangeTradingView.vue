<template>
  <div class="exchange-tradingview-wrap">
    <div id="kline_container" ref="kline_container" class="kline_container"></div>
  </div>
</template>
<script lang="ts" setup>
import { setStorage, getStorage } from '~/utils'
import jstz from 'jstz'
import MonoLoading from '~/components/common/icon-svg/MonoLoading.vue'
import useTradingView from '~/composables/useTradingView'
import useDatafeedAction from '~/composables/useDatafeedAction'
import * as widget1 from '~/public/tradingview/charting_library/charting_library'
console.log(333, widget1)
import { commonStore } from '~/stores/commonStore'
const store = commonStore()
const { pairInfo, isPairDetail, pair } = storeToRefs(store)
const custom_css_url = '/tradingview/tradingview_style/tradingview_custom.css'
const colorMode = useColorMode()
const { locale, t } = useI18n()
const klineNotLoad = ref(false)
const option = useTradingView(colorMode.preference, 'green-up').option[colorMode.preference]
// const { option: {dark: { overrides, studies_overrides, theme }, light: { overrides, studies_overrides, theme } }, tradingviewLangMap}: any = useTradingView(colorMode.preference, 'green-up')
const props = defineProps({
  resolution: {
    type: String,
    default: '15m'
  },
  pair: {
    type: String,
    default: ''
  },
  isShowTechnicalIndicator: {
    type: Boolean,
    default: false
  },
  isShowTradingViewSetting: {
    type: Boolean,
    default: false
  }
})
const resolutionReMap: any = {
  'line': 1,
  '1m': 1,
  '5m': 5,
  '15m': 15,
  '30m': 30,
  '1h': 60,
  '2h': 120,
  '4h': 240,
  '6h': 360,
  '8h': 480,
  '12h': 720,
  '1d': '1d',
  '1w': '1w',
  '1M': '1M'
}
const emit = defineEmits(['closeTechnicalIndicator','closeTradingViewSetting'])
const widgetOption = {
  debug: false,
  symbol: props.pair,
  timezone: 'Asia/Shanghai',
  container: 'kline_container',
  library_path: '/tradingview/charting_library/',
  custom_css_url,
  // saved_data: savedData,
  auto_save_delay: 0.001,
  datafeed: useDatafeedAction({
    pairDecimals: (pairInfo.value[props.pair] || {}).price_scale
  }),
  interval: props.resolution ? resolutionReMap[props.resolution] : '15',
  locale: locale.value,
  autosize: true,
  disabled_features: [
    "header_screenshot",
    "header_symbol_search",
    "header_undo_redo",
    "header_compare",
    "header_chart_type",
    "header_resolutions",
    "header_widget",
    "volume_force_overlay",
    "use_localstorage_for_settings",
    "symbol_search_hot_key",
    'timeframes_toolbar'
  ],
  enabled_features: [
    // "move_logo_to_main_pane",
    "keep_left_toolbar_visible_on_small_screens",
    "save_chart_properties_to_local_storage"
  ],
  toolbar_bg: 'transparent',
  ...option
} as any
let widget: any = null
let datafeedInstance: any = null
const initChart = async (val) => {
  if (widget) {
    widget.remove(); // 先销毁旧图表
    widget = null;
  }
  datafeedInstance = useDatafeedAction({
    pairDecimals: val
  })
  widgetOption.datafeed = datafeedInstance
  // widgetOption.symbol = props.pair.includes('_SWAP') ? props.pair.replace('_SWAP', '').replace('_', '') : props.pair.replace('_', '/');
  widgetOption.interval = props.resolution ? resolutionReMap[props.resolution] : '15',
  widget = new window.TradingView.widget(widgetOption)
  widget.onChartReady(() => {
    widget.activeChart().setChartType(1) // 3 面积  1 正常
    widget.addCustomCSSFile(`/tradingview/tradingview_style/${colorMode.preference}.css`)
  })
}
watch(() => props.resolution, (val) => {
  if (val) {
    widget.activeChart().setResolution(resolutionReMap[val])
  }
})
watch(() => pairInfo.value[props.pair]?.price_scale, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && window.TradingView) {
    setTimeout(() => {
      initChart(newVal)
    }, 100)
  }
}, { immediate: true })
watch(() => colorMode.preference, (val) => {
  widget.changeTheme(colorMode.preference === 'light' ? 'Light' : 'Dark')
  setTimeout(() => {
    const options = useTradingView(val, 'green-up').option[val]
    widget.applyOverrides(options.overrides)
    widget.applyStudiesOverrides(options.studies_overrides)
  }, 10)
  widget.addCustomCSSFile(`/tradingview/tradingview_style/${val}.css`)
})
watch(() => [props.isShowTechnicalIndicator, props.isShowTradingViewSetting], ([TechnicalIndicator, Setting]) => {
  if (TechnicalIndicator && widget) {
    widget.chart().executeActionById("insertIndicator")
    setTimeout(() => {
      emit('closeTechnicalIndicator')
    }, 100)
  } else if (Setting && widget) {
    widget.chart().executeActionById("chartProperties")
    setTimeout(() => {
      emit('closeTradingViewSetting')
    }, 100)
  }
},{ immediate: true })

// 添加币种切换监听
watch(() => pair.value, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget) {
    console.log('币种切换 (pair.value):', oldVal, '->', newVal)
    // 币种切换时清理缓存和状态
    if (datafeedInstance && datafeedInstance.clearCache) {
      datafeedInstance.clearCache()
    }
    if (datafeedInstance && datafeedInstance.setForceRefresh) {
      datafeedInstance.setForceRefresh(true)
    }
    widget.activeChart().setSymbol(newVal)
  }
})

// 监听 props.pair 的变化
watch(() => props.pair, (newVal, oldVal) => {
  if (newVal !== oldVal && newVal !== undefined && widget) {
    console.log('币种切换 (props.pair):', oldVal, '->', newVal)
    // 币种切换时清理缓存和状态
    if (datafeedInstance && datafeedInstance.clearCache) {
      datafeedInstance.clearCache()
    }
    if (datafeedInstance && datafeedInstance.setForceRefresh) {
      datafeedInstance.setForceRefresh(true)
    }
    widget.activeChart().setSymbol(newVal)
  }
})
</script>
<style lang="scss" scoped>
.exchange-tradingview-wrap{
  width:100%;
  height:calc(100% - 46px);
  position:relative;
  .tv-loading {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
    display: flex;
    align-items: center;
    justify-content: center;
    @include bg-color(bg-primary);
    .icon-my-loading {
      font-size: 50px !important;
      @include color(theme);
    }
  }
  .kline_container{
    background: transparent;
    width: 100%;
    height: calc(100%);
  }
}
@include mb{
  .exchange-tradingview-wrap{
    height:calc(100% - 44px);
  }
}
</style>